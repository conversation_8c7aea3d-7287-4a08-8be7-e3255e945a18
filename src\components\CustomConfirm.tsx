import { CheckCircle2, Trash2 } from "lucide-react";
import { useEffect } from "react";
import { toast } from "sonner";

type ActionType = "success" | "delete";

interface CustomToastProps {
  message: string;
  action_type: ActionType;
  resolve: (value: boolean) => void;
  toastId: string | number;
}

function CustomToast({
  message,
  action_type,
  resolve,
  toastId,
}: CustomToastProps) {
  useEffect(() => {
    const root = document.getElementsByTagName("html")?.[0];
    if (root) {
      root.classList.add("skip-outside-click");
    }
    return () => {
      if (root) {
        root.classList.remove("skip-outside-click");
      }
    };
  }, []);

  const handleConfirm = () => {
    toast.dismiss(toastId);
    resolve(true);
  };

  const handleCancel = () => {
    toast.dismiss(toastId);
    resolve(false);
  };

  return (
    <div className="relative mx-auto w-full max-w-[400px]">
      {/* Icon positioned at the top and centered */}
      <div
        className={`absolute -top-4 left-1/2 z-50 flex h-8 w-8 -translate-x-1/2 transform items-center justify-center ${
          action_type === "success" ? "bg-green-500" : "bg-red-500"
        } rounded-full shadow-lg`}
      >
        {action_type === "success" ? (
          <CheckCircle2 className="h-5 w-5 text-white" />
        ) : (
          <Trash2 className="h-5 w-5 text-white" />
        )}{" "}
      </div>

      {/* Message */}
      <p className="pt-6 pb-4 text-center text-sm font-medium text-gray-800">
        {message}
      </p>

      {/* Action buttons */}
      <div className="flex justify-center gap-3 pb-2">
        <button
          type="button"
          className={`rounded-lg px-6 py-2 text-sm font-semibold text-white transition-all duration-200 ${
            action_type === "success"
              ? "bg-green-500 hover:bg-green-600 focus:ring-2 focus:ring-green-300"
              : "bg-red-500 hover:bg-red-600 focus:ring-2 focus:ring-red-300"
          } focus:outline-none`}
          onClick={handleConfirm}
        >
          Yes
        </button>
        <button
          type="button"
          className="rounded-lg bg-gray-100 px-6 py-2 text-sm font-semibold text-gray-700 transition-all duration-200 hover:bg-gray-200 focus:ring-2 focus:ring-gray-300 focus:outline-none"
          onClick={handleCancel}
        >
          No
        </button>
      </div>
    </div>
  );
}

export const customConfirm = (
  message: string = "Are you sure?",
  action_type: ActionType = "success"
): Promise<boolean> =>
  new Promise<boolean>((resolve) => {
    toast.custom(
      (t) => (
        <CustomToast
          message={message}
          action_type={action_type}
          resolve={resolve}
          toastId={t}
        />
      ),
      {
        duration: Infinity,
        position: "top-center",
        className: `w-[90%] max-w-[500px] min-h-[140px] ${
          action_type === "success"
            ? "border-2 border-green-200 bg-white"
            : "border-2 border-red-200 bg-white"
        } rounded-xl shadow-lg overflow-visible`,
        onDismiss: () => {
          const page = document.getElementById("custom-confirm-block");
          page?.classList.remove("pointer-events-none");
        },
      }
    );

    const page = document.getElementById("custom-confirm-block");
    page?.classList.add("pointer-events-none");
  });
