import { Download, RotateCw, X, <PERSON>mIn, ZoomOut } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { Button } from "./ui/button";

interface ImageViewerProps {
  src: string;
  alt: string;
  isOpen: boolean;
  onClose: () => void;
  fileName?: string;
}

export function ImageViewer({
  src,
  alt,
  isOpen,
  onClose,
  fileName,
}: ImageViewerProps) {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [rotation, setRotation] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    if (isOpen) {
      setScale(1);
      setPosition({ x: 0, y: 0 });
      setRotation(0);
      setImageLoaded(false);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  const resetView = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
    setRotation(0);
  };
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case "Escape":
          onClose();
          break;
        case "+":
        case "=":
          setScale((prev) => Math.min(prev * 1.2, 5));
          break;
        case "-":
          setScale((prev) => Math.max(prev / 1.2, 0.1));
          break;
        case "r":
        case "R":
          setRotation((prev) => prev + 90);
          break;
        case "0":
          resetView();
          break;
        case "ArrowLeft":
          setPosition((prev) => ({ ...prev, x: prev.x + 50 }));
          break;
        case "ArrowRight":
          setPosition((prev) => ({ ...prev, x: prev.x - 50 }));
          break;
        case "ArrowUp":
          setPosition((prev) => ({ ...prev, y: prev.y + 50 }));
          break;
        case "ArrowDown":
          setPosition((prev) => ({ ...prev, y: prev.y - 50 }));
          break;
        default:
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale((prev) => Math.min(Math.max(prev * delta, 0.1), 5));
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) return;
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    setPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y,
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    setImageDimensions({
      width: img.naturalWidth,
      height: img.naturalHeight,
    });
    setImageLoaded(true);
  };

  const downloadImage = async () => {
    try {
      const response = await fetch(src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName || `image-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error(`Failed to download image: ${error}`);
      const link = document.createElement("a");
      link.href = src;
      link.download = fileName || `image-${Date.now()}.jpg`;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90">
      <div className="absolute top-4 right-4 z-10 flex gap-2">
        <Button
          onClick={() => setScale((prev) => Math.min(prev * 1.2, 5))}
          className="flex h-10 w-10 items-center justify-center rounded-lg bg-black/60 text-white transition-colors hover:bg-black/80"
          title="Zoom in (+)"
        >
          <ZoomIn className="h-5 w-5" />
        </Button>
        <Button
          onClick={() => setScale((prev) => Math.max(prev / 1.2, 0.1))}
          className="flex h-10 w-10 items-center justify-center rounded-lg bg-black/60 text-white transition-colors hover:bg-black/80"
          title="Zoom out (-)"
        >
          <ZoomOut className="h-5 w-5" />
        </Button>
        <Button
          onClick={() => setRotation((prev) => prev + 90)}
          className="flex h-10 w-10 items-center justify-center rounded-lg bg-black/60 text-white transition-colors hover:bg-black/80"
          title="Rotate (R)"
        >
          <RotateCw className="h-5 w-5" />
        </Button>
        <Button
          onClick={downloadImage}
          className="flex h-10 w-10 items-center justify-center rounded-lg bg-black/60 text-white transition-colors hover:bg-black/80"
          title="Download image"
        >
          <Download className="h-5 w-5" />
        </Button>
        <Button
          onClick={onClose}
          className="flex h-10 w-10 items-center justify-center rounded-lg bg-black/60 text-white transition-colors hover:bg-black/80"
          title="Close (Esc)"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      {imageLoaded && (
        <div className="absolute top-4 left-4 z-10 rounded-lg bg-black/60 px-3 py-2 text-sm text-white">
          <div>
            {imageDimensions.width} × {imageDimensions.height}
          </div>
          <div className="text-xs opacity-80">{alt}</div>
        </div>
      )}

      <div
        className="relative h-full w-full cursor-move overflow-hidden"
        role="button"
        tabIndex={0}
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onClick={(e) => e.target === e.currentTarget && onClose()}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            if (e.target === e.currentTarget) onClose();
          }
        }}
      >
        <img
          src={src}
          alt={alt}
          className={`absolute top-1/2 left-1/2 max-h-none max-w-none transition-transform duration-200 select-none ${
            isDragging ? "cursor-grabbing" : "cursor-grab"
          }`}
          style={{
            transform: `translate(-50%, -50%) translate(${position.x}px, ${position.y}px) scale(${scale}) rotate(${rotation}deg)`,
            transformOrigin: "center",
          }}
          draggable={false}
          onLoad={handleImageLoad}
        />
      </div>
      <div className="absolute bottom-4 left-4 rounded-lg bg-black/60 px-3 py-2 text-sm text-white">
        <div className="flex items-center gap-4">
          <span>{Math.round(scale * 100)}%</span>
          {rotation !== 0 && <span>{rotation}°</span>}
        </div>
        <div className="mt-1 text-xs opacity-80">
          Use mouse wheel to zoom • Arrow keys to pan • R to rotate
        </div>
      </div>
    </div>
  );
}
