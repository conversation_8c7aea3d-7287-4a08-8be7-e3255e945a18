import * as React from "react";

import { Icon } from "@/components/icons/Icon";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

import Select from "./ui/select";

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems?: number;
  pageSize?: number;
  pageSizeOptions?: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  showPageSizeSelector?: boolean;
  showItemsInfo?: boolean;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
  disabled?: boolean;
  className?: string;
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
}

export function Pagination({
  currentPage,
  totalPages,
  totalItems,
  pageSize = 10,
  pageSizeOptions = [10, 20, 50, 100],
  onPageChange,
  onPageSizeChange,
  showPageSizeSelector = true,
  showItemsInfo = true,
  showFirstLast = true,
  maxVisiblePages = 7,
  disabled = false,
  className,
  size = "default",
  variant = "outline",
}: PaginationProps) {
  const startItem = totalItems ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = totalItems ? Math.min(currentPage * pageSize, totalItems) : 0;

  const getVisiblePages = React.useMemo(() => {
    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const halfVisible = Math.floor(maxVisiblePages / 2);
    let start = Math.max(1, currentPage - halfVisible);
    const end = Math.min(totalPages, start + maxVisiblePages - 1);

    if (end - start + 1 < maxVisiblePages) {
      start = Math.max(1, end - maxVisiblePages + 1);
    }

    const pages = [];
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }, [currentPage, totalPages, maxVisiblePages]);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage && !disabled) {
      onPageChange(page);
    }
  };

  const handlePageSizeChange = (newPageSize: string) => {
    if (onPageSizeChange && !disabled) {
      const size = parseInt(newPageSize, 10);
      onPageSizeChange(size);
      const newTotalPages = totalItems
        ? Math.ceil(totalItems / size)
        : totalPages;
      if (currentPage > newTotalPages) {
        onPageChange(newTotalPages);
      }
    }
  };

  const sizeClasses = {
    sm: "h-8 px-2 text-xs",
    default: "h-9 px-3 text-sm",
    lg: "h-10 px-4 text-base",
  };

  if (totalPages <= 1 && !showItemsInfo && !showPageSizeSelector) {
    return null;
  }

  return (
    <div className={cn("flex items-center justify-between gap-4", className)}>
      {showItemsInfo && totalItems && (
        <div className="text-muted-foreground text-sm">
          Showing {startItem} to {endItem} of {totalItems} items
        </div>
      )}

      <div className="flex items-center gap-2">
        {showFirstLast && currentPage > 1 && (
          <Button
            variant={variant}
            size={size}
            onClick={() => handlePageChange(1)}
            disabled={disabled}
            className={cn(sizeClasses[size])}
          >
            <Icon name="back" className="h-4 w-4" />
            <Icon name="back" className="-ml-2 h-4 w-4" />
          </Button>
        )}

        <Button
          variant={variant}
          size={size}
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={disabled || currentPage <= 1}
          className={cn(sizeClasses[size])}
        >
          <Icon name="back" className="h-4 w-4" />
        </Button>

        <div className="flex items-center gap-1">
          {getVisiblePages.map((page) => (
            <Button
              key={page}
              variant={page === currentPage ? "default" : variant}
              size={size}
              onClick={() => handlePageChange(page)}
              disabled={disabled}
              className={cn(
                sizeClasses[size],
                page === currentPage && "pointer-events-none"
              )}
            >
              {page}
            </Button>
          ))}
        </div>

        <Button
          variant={variant}
          size={size}
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={disabled || currentPage >= totalPages}
          className={cn(sizeClasses[size])}
        >
          <Icon name="forward" className="h-4 w-4" />
        </Button>

        {showFirstLast && currentPage < totalPages && (
          <Button
            variant={variant}
            size={size}
            onClick={() => handlePageChange(totalPages)}
            disabled={disabled}
            className={cn(sizeClasses[size])}
          >
            <Icon name="forward" className="h-4 w-4" />
            <Icon name="forward" className="-ml-2 h-4 w-4" />
          </Button>
        )}
      </div>

      {showPageSizeSelector && onPageSizeChange && (
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">Items per page:</span>
          <Select
            value={pageSize.toString()}
            onChange={(e) => handlePageSizeChange(e.target.value)}
            disabled={disabled}
            options={pageSizeOptions.map((option) => ({
              value: option.toString(),
              label: option.toString(),
            }))}
            className="w-20"
            variant={variant}
            size={size === "sm" ? "small" : size === "lg" ? "large" : "default"}
          />{" "}
        </div>
      )}
    </div>
  );
}
