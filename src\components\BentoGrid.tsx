import { ReactNode, useEffect, useState } from "react";

import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

type ScreenSize =
  | "mobile"
  | "mobile-lg"
  | "tablet"
  | "tablet-lg"
  | "desktop-sm"
  | "desktop";

interface BentoGridProps {
  children: ReactNode;
  className?: string;
}

interface BentoGridItemProps {
  children: ReactNode;
  className?: string;
  span?: number;
  rowSpan?: number;
  index?: number;
  title?: string;
  subtitle?: string;
}

function BentoGrid({ children, className = "" }: BentoGridProps) {
  const [screenSize, setScreenSize] = useState<ScreenSize>("desktop");

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width <= 480) {
        setScreenSize("mobile");
      } else if (width <= 640) {
        setScreenSize("mobile-lg");
      } else if (width <= 768) {
        setScreenSize("tablet");
      } else if (width <= 1024) {
        setScreenSize("tablet-lg");
      } else if (width <= 1400) {
        setScreenSize("desktop-sm");
      } else {
        setScreenSize("desktop");
      }
    };

    updateScreenSize();
    window.addEventListener("resize", updateScreenSize);
    return () => window.removeEventListener("resize", updateScreenSize);
  }, []);

  const getGridClasses = (): string => {
    const baseClasses = "grid w-full";

    switch (screenSize) {
      case "mobile":
        return `${baseClasses} grid-cols-1 gap-3 auto-rows-[minmax(200px,auto)]`;
      case "mobile-lg":
        return `${baseClasses} grid-cols-1 gap-3 auto-rows-[minmax(220px,auto)]`;
      case "tablet":
        return `${baseClasses} grid-cols-2 gap-4 auto-rows-[minmax(240px,auto)]`;
      case "tablet-lg":
        return `${baseClasses} grid-cols-2 gap-4 auto-rows-[minmax(260px,auto)]`;
      case "desktop-sm":
        return `${baseClasses} grid-cols-3 gap-5 auto-rows-[minmax(280px,auto)]`;
      default:
        return `${baseClasses} grid-cols-4 gap-6 auto-rows-[minmax(300px,auto)]`;
    }
  };

  return (
    <div
      className={cn(getGridClasses(), className)}
      data-screen-size={screenSize}
    >
      {children}
    </div>
  );
}

function BentoGridItem({
  children,
  className = "",
  span = 1,
  rowSpan = 1,
  index = 0,
  title,
  subtitle,
}: BentoGridItemProps) {
  const [screenSize, setScreenSize] = useState<ScreenSize>("desktop");

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width <= 480) {
        setScreenSize("mobile");
      } else if (width <= 640) {
        setScreenSize("mobile-lg");
      } else if (width <= 768) {
        setScreenSize("tablet");
      } else if (width <= 1024) {
        setScreenSize("tablet-lg");
      } else if (width <= 1400) {
        setScreenSize("desktop-sm");
      } else {
        setScreenSize("desktop");
      }
    };

    updateScreenSize();
    window.addEventListener("resize", updateScreenSize);
    return () => window.removeEventListener("resize", updateScreenSize);
  }, []);

  const getResponsiveSpan = (): string => {
    switch (screenSize) {
      case "mobile":
      case "mobile-lg":
        return "col-span-1"; // Always single column on mobile
      case "tablet":
        return span >= 3
          ? "col-span-2"
          : span >= 2
            ? "col-span-2"
            : "col-span-1";
      case "tablet-lg":
        return span >= 3
          ? "col-span-2"
          : span >= 2
            ? "col-span-2"
            : "col-span-1";
      case "desktop-sm":
        return span >= 4
          ? "col-span-3"
          : span >= 3
            ? "col-span-3"
            : span >= 2
              ? "col-span-2"
              : "col-span-1";
      default:
        return `col-span-${span}`;
    }
  };

  const getResponsiveRowSpan = (): string => {
    if (screenSize === "mobile" || screenSize === "mobile-lg") {
      return "row-span-1"; // Always single row on mobile
    }
    return rowSpan > 1 ? `row-span-${rowSpan}` : "row-span-1";
  };

  const getResponsivePadding = (): string => {
    switch (screenSize) {
      case "mobile":
        return "p-3";
      case "mobile-lg":
      case "tablet":
        return "p-4";
      default:
        return "p-5";
    }
  };

  return (
    <div
      className={cn(
        getResponsiveSpan(),
        getResponsiveRowSpan(),
        "transition-all duration-300 hover:scale-[1.02] hover:shadow-lg",
        screenSize === "mobile" || screenSize === "mobile-lg"
          ? "hover:scale-[1.01]"
          : "",
        className
      )}
      style={{
        animationDelay: `${index * 100}ms`,
        animationFillMode: "both",
        animationName: "fadeInScale",
        animationDuration: "500ms",
      }}
    >
      <Card className="h-full border-0 shadow-md transition-all duration-300 hover:shadow-lg">
        <CardContent
          className={cn("flex h-full flex-col", getResponsivePadding())}
        >
          {(title || subtitle) && (
            <div className="mb-4">
              {title && (
                <h3
                  className={cn(
                    "text-foreground mb-1 font-semibold",
                    screenSize === "mobile" ? "text-base" : "text-lg"
                  )}
                >
                  {title}
                </h3>
              )}
              {subtitle && (
                <p
                  className={cn(
                    "text-muted-foreground",
                    screenSize === "mobile" ? "text-xs" : "text-sm"
                  )}
                >
                  {subtitle}
                </p>
              )}
            </div>
          )}
          <div className="flex flex-1 flex-col">{children}</div>
        </CardContent>
      </Card>
    </div>
  );
}

BentoGrid.Item = BentoGridItem;

export default BentoGrid;
