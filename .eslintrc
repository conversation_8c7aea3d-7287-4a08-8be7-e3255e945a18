{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:@typescript-eslint/recommended", "airbnb", "airbnb-typescript", "plugin:import/typescript", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["react", "@typescript-eslint", "prettier"], "rules": {"react/prop-types": "off", "react/react-in-jsx-scope": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/naming-convention": ["error", {"selector": "variable", "format": ["camelCase", "PascalCase", "UPPER_CASE", "snake_case"], "filter": {"regex": "^[a-zA-Z_]+$", "match": false}}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "no-console": "error", "no-unsafe-optional-chaining": "off", "no-shadow": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "react/require-default-props": "off", "no-plusplus": "off", "import/extensions": "off", "@typescript-eslint/no-shadow": "off", "react/jsx-props-no-spreading": "off", "import/prefer-default-export": "off", "react/jsx-no-useless-fragment": "off", "no-underscore-dangle": "off", "no-nested-ternary": "off", "import/no-named-as-default": "off", "no-alert": "off", "jsx-a11y/label-has-associated-control": ["error", {"labelAttributes": ["label"], "controlComponents": ["Input", "Select", "Textarea"], "depth": 3, "required": {"some": ["nesting", "id"]}}], "no-promise-executor-return": "off"}}