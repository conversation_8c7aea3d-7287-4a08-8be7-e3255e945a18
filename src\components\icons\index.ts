import {
  AlertTriangle,
  Calendar,
  Check,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Clock,
  Copy,
  Download,
  Edit3,
  ExternalLink,
  Eye,
  EyeOff,
  File,
  FileAudio,
  FileImage,
  FileSpreadsheet,
  FileText,
  FileVideo,
  Filter,
  Home,
  Info,
  Mail,
  Menu,
  MoreHorizontal,
  MoreVertical,
  Phone,
  Plus,
  Save,
  Search,
  Settings,
  SortAsc,
  SortDesc,
  Trash2,
  Upload,
  User,
  X,
  XCircle,
  ZoomIn,
  ZoomOut,
} from "lucide-react";

export const Icons = {
  delete: Trash2,
  edit: Edit3,
  add: Plus,
  save: Save,
  cancel: X,
  confirm: Check,
  search: Search,
  copy: Copy,

  show: Eye,
  hide: EyeOff,

  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,

  home: Home,
  menu: Menu,
  back: ChevronLeft,
  forward: ChevronRight,
  up: ChevronUp,
  down: ChevronDown,

  user: User,
  settings: Settings,

  download: Download,
  upload: Upload,
  filter: Filter,
  sortAsc: SortAsc,
  sortDesc: SortDesc,

  file: File,
  fileText: FileText,
  fileImage: FileImage,
  fileVideo: FileVideo,
  fileAudio: FileAudio,
  fileSpreadsheet: FileSpreadsheet,

  external: ExternalLink,
  email: Mail,
  phone: Phone,
  calendar: Calendar,
  time: Clock,
  moreVertical: MoreVertical,
  moreHorizontal: MoreHorizontal,
  zoomIn: ZoomIn,
  zoomOut: ZoomOut,
} as const;

export type IconName = keyof typeof Icons;
