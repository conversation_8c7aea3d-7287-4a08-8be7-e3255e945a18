import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Plus } from "lucide-react";
import { ComponentProps } from "react";

import { Button } from "../../components/ui/button";

type StoryProps = ComponentProps<typeof Button> & {
  buttonText?: string;
};

const meta: Meta<StoryProps> = {
  title: "UI/Button",
  component: Button,
  args: { buttonText: "Button" },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: [
        "default",
        "destructive",
        "outline",
        "secondary",
        "ghost",
        "link",
      ],
    },
    size: {
      control: "select",
      options: ["default", "sm", "lg", "icon"],
    },
  },
  render: (args) => (
    <Button {...args}>{args.buttonText ?? args.children}</Button>
  ),
};

export default meta;

type Story = StoryObj<StoryProps>;

export const Primary: Story = { args: { variant: "default" } };
export const Destructive: Story = { args: { variant: "destructive" } };
export const Outline: Story = { args: { variant: "outline" } };
export const Secondary: Story = { args: { variant: "secondary" } };
export const Ghost: Story = { args: { variant: "ghost" } };
export const Link: Story = { args: { variant: "link" } };
export const Large: Story = { args: { size: "lg" } };
export const Small: Story = { args: { size: "sm" } };
export const Icon: Story = {
  args: { size: "icon", children: <Plus />, buttonText: undefined },
};
