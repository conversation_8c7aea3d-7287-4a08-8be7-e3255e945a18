{"name": "optiwise-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port 3000", "start": "vite --port 3000", "build": "tsc -b && vite build", "preview": "vite preview", "pretty": "prettier --write \"**/*.{js,jsx,ts,tsx,json}\"", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint --fix .", "tsc": "tsc", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.13", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "^9.10.0", "react-dom": "^19.1.1", "react-router": "^7.9.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "vaul": "^1.1.2"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@eslint/js": "^9.33.0", "@storybook/addon-a11y": "^9.1.6", "@storybook/addon-docs": "^9.1.6", "@storybook/addon-vitest": "^9.1.6", "@storybook/react-vite": "^9.1.6", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^24.4.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.1.6", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.6", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^9.1.6", "tw-animate-css": "^1.3.8", "typescript": "~5.4.4", "typescript-eslint": "^8.30.1", "vite": "^7.1.2", "vitest": "^3.2.4", "@vitest/browser": "^3.2.4", "playwright": "^1.55.0", "@vitest/coverage-v8": "^3.2.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}