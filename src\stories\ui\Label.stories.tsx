import type { Meta, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Label } from "../../components/ui/label";

type StoryProps = ComponentProps<typeof Label> & {
  labelText?: string;
};

const meta: Meta<StoryProps> = {
  title: "UI/Label",
  component: Label,
  tags: ["autodocs"],
  args: {
    children: "Label",
    labelText: "Label",
  },
};
export default meta;

type Story = StoryObj<StoryProps>;

export const Default: Story = {
  render: (args) => <Label {...args}>{args.labelText}</Label>,
};
