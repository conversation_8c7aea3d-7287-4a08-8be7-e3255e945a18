import type { Preview } from "@storybook/react-vite";

import "../src/index.css";

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
      exclude: [
        "asChild",
        "ref",
        "key",
        "className",
        "style",
        "children",
        "id",
        "name",
        "value",
        "onChange",
        "onClick",
        "onBlur",
        "onFocus",
        "onKeyDown",
        "onKeyUp",
        "onMouseEnter",
        "onMouseLeave",
        "onSubmit",
      ], // Exclude common props to reduce noise in the controls panel of Storybook
    },
    docs: {
      autodocs: "tag",
    },
    a11y: {
      test: "todo",
    },
  },
};

export default preview;
