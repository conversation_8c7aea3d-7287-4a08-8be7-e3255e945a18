import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { ComponentProps, useState } from "react";

import Modal from "@/components/Modal";
import { Button } from "@/components/ui/button";

type StoryProps = ComponentProps<typeof Modal>;

function ModalWrapper(args: StoryProps) {
  const { open: initialOpen } = args;
  const [open, setOpen] = useState(initialOpen || false);

  return (
    <div>
      <Button onClick={() => setOpen(true)}>Open Modal</Button>
      <Modal {...args} open={open} onOpenChange={setOpen} />
    </div>
  );
}

const meta: Meta<StoryProps> = {
  title: "Modal",
  component: Modal,
  tags: ["autodocs"],
  args: {
    open: false, // closed by default on Docs page
    title: "Confirm Action",
    description: "Are you sure you want to continue?",
    children: <p>This is some modal content.</p>,
    showFooter: true,
    submitText: "Confirm",
    cancelText: "Cancel",
    submitVariant: "default",
    size: "md",
    closeOnOverlayClick: true,
    showCloseButton: true,
    isLoading: false,
    onSubmit: () => {},
  },
  argTypes: {
    footerContent: { table: { disable: true } },
    onOpenChange: { table: { disable: true } },
    onCancel: { table: { disable: true } },
    size: {
      control: "select",
      options: ["sm", "md", "lg", "xl", "full"],
    },
    submitVariant: {
      control: "select",
      options: [
        "default",
        "destructive",
        "outline",
        "secondary",
        "ghost",
        "link",
      ],
    },
  },
  render: (args) => <ModalWrapper {...args} />,
};

export default meta;

type Story = StoryObj<StoryProps>;

// Helper to auto-open modal in Canvas
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const autoOpen = async ({ canvasElement }: any) => {
  canvasElement.querySelector("button")?.click();
};

export const Default: Story = {
  play: autoOpen,
};

export const WithFooterContent: Story = {
  args: {
    footerContent: (
      <div className="flex w-full justify-between">
        <span className="text-muted-foreground text-sm">Custom footer</span>
        <Button>Custom Action</Button>
      </div>
    ),
  },
  play: autoOpen,
};

export const WithoutCancel: Story = {
  args: { showCancel: false },
  play: autoOpen,
};

export const LoadingState: Story = {
  args: {
    isLoading: true,
    submitText: "Processing",
  },
  play: autoOpen,
};

export const OverlayLocked: Story = {
  args: {
    closeOnOverlayClick: false,
    description:
      "This modal cannot be closed by clicking outside. Use the close button instead.",
  },
  play: autoOpen,
};

export const NoFooter: Story = {
  args: { showFooter: false },
  play: autoOpen,
};

export const NoCloseButton: Story = {
  args: { showCloseButton: false },
  play: autoOpen,
};
