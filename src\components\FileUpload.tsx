import * as React from "react";
import { toast } from "sonner";

import { ImageViewer } from "@/components/ImageViewer";
import { Icon } from "@/components/icons/Icon";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn, formatFileSize } from "@/lib/utils";

export interface FileUploadFile {
  id: string;
  file: File;
  progress?: number;
  status: "pending" | "uploading" | "success" | "error";
  error?: string;
  url?: string;
}

export interface FileUploadProps {
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number; // in bytes
  minSize?: number; // in bytes

  onFilesChange?: (files: FileUploadFile[]) => void;
  onUpload?: (files: File[]) => Promise<void>;
  autoUpload?: boolean;
  validator?: (file: File) => string | null;
  className?: string;
  disabled?: boolean;
  variant?: "default" | "outline" | "dashed";
  size?: "sm" | "default" | "lg";
  title?: string;
  description?: string;
  uploadText?: string;
  browseText?: string;
  enableDragDrop?: boolean;
  showPreview?: boolean;
  previewType?: "list" | "grid";
  showProgress?: boolean;
  defaultFiles?: FileUploadFile[];
  value?: FileUploadFile[];
}

export function FileUpload({
  accept,
  multiple = false,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  minSize = 0,
  onFilesChange,
  onUpload,
  autoUpload = false,
  validator,
  className,
  disabled = false,
  variant = "dashed",
  size = "default",
  title = "Upload files",
  description = "Drag and drop files here or click to browse",
  uploadText = "Upload",
  browseText = "Browse files",
  enableDragDrop = true,
  showPreview = true,
  previewType = "list",
  showProgress = true,
  defaultFiles = [],
  value,
}: FileUploadProps) {
  const [files, setFiles] = React.useState<FileUploadFile[]>(defaultFiles);
  const [isDragOver, setIsDragOver] = React.useState(false);
  const [imageViewer, setImageViewer] = React.useState<{
    src: string;
    alt: string;
  } | null>(null);
  const [imagePreviews, setImagePreviews] = React.useState<
    Record<string, string>
  >({});
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const isControlled = value !== undefined;
  const currentFiles = isControlled ? value : files;

  const handleFilesChange = (newFiles: FileUploadFile[]) => {
    if (!isControlled) {
      setFiles(newFiles);
    }
    onFilesChange?.(newFiles);
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File size must be less than ${formatFileSize(maxSize)}`;
    }
    if (file.size < minSize) {
      return `File size must be at least ${formatFileSize(minSize)}`;
    }
    if (validator) {
      return validator(file);
    }
    return null;
  };
  const handleUpload = async (filesToUpload?: File[]) => {
    if (!onUpload) return;

    const targetFiles =
      filesToUpload ||
      currentFiles.filter((f) => f.status === "pending").map((f) => f.file);

    if (targetFiles.length === 0) return;

    const updatedFiles = currentFiles.map((f) =>
      targetFiles.includes(f.file)
        ? { ...f, status: "uploading" as const, progress: 0 }
        : f
    );
    handleFilesChange(updatedFiles);

    try {
      await onUpload(targetFiles);

      const successFiles = currentFiles.map((f) =>
        targetFiles.includes(f.file)
          ? { ...f, status: "success" as const, progress: 100 }
          : f
      );
      handleFilesChange(successFiles);
    } catch (error) {
      const errorFiles = currentFiles.map((f) =>
        targetFiles.includes(f.file)
          ? { ...f, status: "error" as const, error: String(error) }
          : f
      );
      handleFilesChange(errorFiles);
    }
  };

  const processFiles = async (fileList: FileList) => {
    const newFiles: FileUploadFile[] = [];

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const error = validateFile(file);

      if (currentFiles.length + newFiles.length >= maxFiles) {
        break;
      }

      const fileUpload: FileUploadFile = {
        id: `${Date.now()}-${i}`,
        file,
        status: error ? "error" : "pending",
        error: error ?? undefined,
      };

      newFiles.push(fileUpload);
    }

    const updatedFiles = [...currentFiles, ...newFiles];
    handleFilesChange(updatedFiles);

    if (autoUpload && onUpload) {
      const validFiles = newFiles
        .filter((f) => f.status === "pending")
        .map((f) => f.file);
      if (validFiles.length > 0) {
        await handleUpload(validFiles);
      }
    }
  };

  const removeFile = (id: string) => {
    const updatedFiles = currentFiles.filter((f) => f.id !== id);
    handleFilesChange(updatedFiles);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && enableDragDrop) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled || !enableDragDrop) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      processFiles(selectedFiles);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const sizeClasses = {
    sm: "p-4 text-sm",
    default: "p-6 text-base",
    lg: "p-8 text-lg",
  };

  const variantClasses = {
    default: "border-solid bg-background",
    outline: "border-solid bg-background",
    dashed: "border-dashed bg-muted/20",
  };

  const getStatusIcon = (status: FileUploadFile["status"]) => {
    switch (status) {
      case "pending":
        return <Icon name="time" className="text-muted-foreground h-4 w-4" />;
      case "uploading":
        return (
          <Icon name="time" className="h-4 w-4 animate-spin text-blue-500" />
        );
      case "success":
        return <Icon name="success" className="h-4 w-4 text-green-500" />;
      case "error":
        return <Icon name="error" className="h-4 w-4 text-red-500" />;
      default:
        return <Icon name="time" className="text-muted-foreground h-4 w-4" />;
    }
  };

  const getStatusColor = (status: FileUploadFile["status"]) => {
    switch (status) {
      case "pending":
        return "secondary";
      case "uploading":
        return "outline";
      case "success":
        return "default";
      case "error":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getFileTypeIcon = (file: File) => {
    const type = file.type.toLowerCase();
    const extension = file.name.split(".").pop()?.toLowerCase();

    if (type.startsWith("image/")) return "fileImage";
    if (type.startsWith("video/")) return "fileVideo";
    if (type.startsWith("audio/")) return "fileAudio";
    if (
      type.includes("spreadsheet") ||
      ["xlsx", "xls", "csv"].includes(extension || "")
    )
      return "fileSpreadsheet";
    if (
      type.includes("text") ||
      ["txt", "md", "json"].includes(extension || "")
    )
      return "fileText";
    return "file";
  };

  const isImageFile = (file: File) => {
    return file.type.startsWith("image/");
  };

  const createImagePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.readAsDataURL(file);
    });
  };

  React.useEffect(() => {
    const loadImagePreviews = async () => {
      const filesToPreview = currentFiles.filter(
        (fileUpload) =>
          isImageFile(fileUpload.file) && !imagePreviews[fileUpload.id]
      );

      if (filesToPreview.length === 0) return;

      const previewPromises = filesToPreview.map(async (fileUpload) => {
        try {
          const preview = await createImagePreview(fileUpload.file);
          return { id: fileUpload.id, preview };
        } catch (error) {
          toast.error(
            `Failed to create image preview: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
          return null;
        }
      });

      const results = await Promise.all(previewPromises);
      const newPreviews: Record<string, string> = {};
      results.forEach((result) => {
        if (result) {
          newPreviews[result.id] = result.preview;
        }
      });

      if (Object.keys(newPreviews).length > 0) {
        setImagePreviews((prev) => ({ ...prev, ...newPreviews }));
      }
    };

    loadImagePreviews();
  }, [currentFiles, imagePreviews]);

  const pendingFiles = currentFiles.filter((f) => f.status === "pending");

  return (
    <div className={cn("space-y-4", className)}>
      <div
        role="button"
        tabIndex={disabled ? -1 : 0}
        aria-disabled={disabled}
        className={cn(
          "border-input relative flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 transition-colors",
          sizeClasses[size],
          variantClasses[variant],
          isDragOver && "border-primary bg-primary/5",
          disabled && "cursor-not-allowed opacity-50"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
        onKeyDown={(e) => {
          if (!disabled && (e.key === "Enter" || e.key === " ")) {
            e.preventDefault();
            fileInputRef.current?.click();
          }
        }}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileInputChange}
          disabled={disabled}
          className="hidden"
        />

        <Icon name="upload" className="text-muted-foreground mb-2 h-8 w-8" />
        <h3 className="font-medium">{title}</h3>
        <p className="text-muted-foreground text-sm">{description}</p>

        <div className="mt-4 flex gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            disabled={disabled}
            onClick={(e) => {
              e.stopPropagation();
              fileInputRef.current?.click();
            }}
          >
            {browseText}
          </Button>

          {!autoUpload && pendingFiles.length > 0 && (
            <Button
              type="button"
              size="sm"
              disabled={disabled}
              onClick={(e) => {
                e.stopPropagation();
                handleUpload();
              }}
            >
              {uploadText} ({pendingFiles.length})
            </Button>
          )}
        </div>

        <p className="text-muted-foreground mt-2 text-xs">
          Max {maxFiles} files, up to {formatFileSize(maxSize)} each
        </p>
      </div>

      {showPreview && currentFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Files ({currentFiles.length})</h4>

          <div
            className={cn(
              previewType === "grid"
                ? "grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4"
                : "space-y-2"
            )}
          >
            {currentFiles.map((fileUpload) => {
              const isImage = isImageFile(fileUpload.file);
              const preview = imagePreviews[fileUpload.id];

              return (
                <div
                  key={fileUpload.id}
                  className="border-input flex items-center gap-3 rounded-lg border p-3"
                >
                  <div className="shrink-0">
                    {isImage && preview ? (
                      <div
                        className="relative h-12 w-12 cursor-pointer overflow-hidden rounded-lg border"
                        onClick={() =>
                          setImageViewer({
                            src: preview,
                            alt: fileUpload.file.name,
                          })
                        }
                      >
                        <img
                          src={preview}
                          alt={fileUpload.file.name}
                          className="h-full w-full object-cover"
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black/0 opacity-0 transition-opacity hover:bg-black/20 hover:opacity-100">
                          <Icon name="zoomIn" className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    ) : (
                      <div className="bg-muted flex h-12 w-12 items-center justify-center rounded-lg">
                        <Icon
                          name={getFileTypeIcon(fileUpload.file)}
                          className="text-muted-foreground h-6 w-6"
                        />
                      </div>
                    )}
                  </div>

                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-medium">
                      {fileUpload.file.name}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {formatFileSize(fileUpload.file.size)}
                    </p>

                    {fileUpload.error && (
                      <p className="text-xs text-red-500">{fileUpload.error}</p>
                    )}

                    {showProgress && fileUpload.status === "uploading" && (
                      <div className="bg-muted mt-1 h-1 w-full rounded-full">
                        <div
                          className="bg-primary h-1 rounded-full transition-all"
                          style={{ width: `${fileUpload.progress || 0}%` }}
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex shrink-0 items-center gap-2">
                    <div className="shrink-0">
                      {getStatusIcon(fileUpload.status)}
                    </div>

                    <Badge
                      variant={getStatusColor(fileUpload.status)}
                      className="text-xs"
                    >
                      {fileUpload.status}
                    </Badge>

                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(fileUpload.id)}
                      disabled={disabled}
                      className="h-6 w-6 p-0"
                    >
                      <Icon name="cancel" className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      <ImageViewer
        src={imageViewer?.src || ""}
        alt={imageViewer?.alt || ""}
        isOpen={!!imageViewer}
        onClose={() => setImageViewer(null)}
      />
    </div>
  );
}
