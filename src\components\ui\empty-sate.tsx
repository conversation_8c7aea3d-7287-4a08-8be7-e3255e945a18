import clsx from "clsx";
import { ReactNode } from "react";

interface EmptyStateProps {
  title?: string;
  description?: string;
  icon?: ReactNode;
  action?: ReactNode;
  className?: string;
}

function EmptyState({
  title = "No data found",
  description = "There is no data to display at this time.",
  icon,
  action,
  className = "",
}: EmptyStateProps) {
  return (
    <div
      className={clsx(
        "flex flex-col items-center justify-center p-8 text-center",
        "mx-auto max-w-md",
        className
      )}
      role="status"
    >
      {icon && (
        <div className="mb-4 flex items-center justify-center text-gray-400">
          {icon}
        </div>
      )}
      <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-gray-100">
        {title}
      </h3>
      <p className="mb-4 text-sm text-gray-500 dark:text-gray-400">
        {description}
      </p>
      {action && <div className="mt-2">{action}</div>}
    </div>
  );
}

export default EmptyState;
