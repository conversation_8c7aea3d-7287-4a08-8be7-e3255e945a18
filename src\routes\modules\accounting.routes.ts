import type { RouteObject } from "react-router";

// import { lazyLoad } from "../../lib/lazy";
// const AccountingLayout = lazyLoad(() => import("../../layouts/AccountingLayout"));
// const Ledger = lazyLoad(() => import("../../pages/accounting/Ledger"));

export const accountingRoutes: RouteObject[] = [
  // {
  //   path: "/accounting",
  //   element: <AccountingLayout />,
  //   children: [
  //     { path: "ledger", element: <Ledger /> },
  //   ],
  // },
];
