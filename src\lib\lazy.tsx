import { Suspense, lazy } from "react";

import { FullScreenLoading } from "@/components/Loader";

export function lazyLoad(
  importFunc: () => Promise<{ default: React.ComponentType }>
) {
  const LazyComponent = lazy(importFunc);

  return function WrappedComponent(props: Record<string, unknown>) {
    return (
      <Suspense fallback={<FullScreenLoading variant="logo" />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}
