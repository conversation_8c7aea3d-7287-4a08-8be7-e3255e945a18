import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Calendar } from "@/components/ui/calendar";

type StoryProps = ComponentProps<typeof Calendar>;

const meta: Meta<StoryProps> = {
  title: "UI/Calendar",
  component: Calendar,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  args: {
    mode: "single",
    selected: new Date(),
  },
};

export default meta;
type Story = StoryObj<StoryProps>;

// Default calendar
export const Default: Story = {};

// With multiple selection
export const Multiple: Story = {
  args: {
    mode: "multiple",
    selected: [new Date("2025-09-05"), new Date("2025-09-10")],
  },
};

// With range selection
export const Range: Story = {
  args: {
    mode: "range",
    selected: {
      from: new Date("2025-09-05"),
      to: new Date("2025-09-12"),
    },
  },
};

// With dropdown navigation
export const WithDropdowns: Story = {
  args: {
    captionLayout: "dropdown",
  },
};

// With disabled dates
export const DisabledDates: Story = {
  args: {
    disabled: [{ from: new Date(2025, 8, 10), to: new Date(2025, 8, 20) }],
  },
};

export const DifferentButtonVariant: Story = {
  render: (args: StoryProps) => (
    <div className="flex flex-wrap justify-center gap-4">
      <Calendar {...args} buttonVariant="default" />
      <Calendar {...args} buttonVariant="outline" />
      <Calendar {...args} buttonVariant="secondary" />
      <Calendar {...args} buttonVariant="ghost" />
      <Calendar {...args} buttonVariant="link" />
      <Calendar {...args} buttonVariant="destructive" />
    </div>
  ),
};
