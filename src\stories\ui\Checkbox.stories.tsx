import type { Meta, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Checkbox } from "../../components/ui/checkbox";

type StoryProps = ComponentProps<typeof Checkbox>;

const meta: Meta<StoryProps> = {
  title: "UI/Checkbox",
  component: Checkbox,
  args: {
    checked: true,
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<StoryProps>;

export const Unchecked: Story = { args: { checked: false } };
export const Checked: Story = { args: { checked: true } };
export const Disabled: Story = {
  args: { disabled: true, checked: true },
};
export const Invalid: Story = {
  args: { "aria-invalid": true, checked: false },
};
