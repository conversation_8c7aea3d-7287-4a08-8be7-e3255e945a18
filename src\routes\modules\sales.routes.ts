import type { RouteObject } from "react-router";

// import { lazyLoad } from "../../lib/lazy";

// const SalesLayout = lazyLoad(() => import("../../layouts/SalesLayout"));
// const Orders = lazyLoad(() => import("../../pages/sales/Orders"));

export const salesRoutes: RouteObject[] = [
  // {
  //   path: "/sales",
  //   element: <SalesLayout />,
  //   children: [
  //     { path: "orders", element: <Orders /> },
  //   ],
  // },
];
