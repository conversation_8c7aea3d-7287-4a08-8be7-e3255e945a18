import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Badge } from "../../components/ui/badge";

type StoryProps = ComponentProps<typeof Badge>;

const meta: Meta<StoryProps> = {
  title: "UI/Badge",
  component: Badge,
  tags: ["autodocs"],
  args: {
    children: "Badge",
  },
  argTypes: {
    variant: {
      control: "select",
      options: ["default", "secondary", "destructive", "outline"],
    },
  },
};
export default meta;

type Story = StoryObj<StoryProps>;

export const Default: Story = {};
export const Secondary: Story = { args: { variant: "secondary" } };
export const Destructive: Story = { args: { variant: "destructive" } };
export const Outline: Story = { args: { variant: "outline" } };
