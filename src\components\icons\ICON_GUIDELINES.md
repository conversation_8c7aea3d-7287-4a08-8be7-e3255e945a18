# Icon Usage Guidelines

##  Use Centralized Icons Only

All developers MUST use the centralized icon system. Direct imports from `lucide-react` are forbidden.

## ✅ Correct Usage

```tsx
import { Icon } from '@/components/icons/Icon';

// For delete operations - ALWAYS use 'delete'
<Icon name="delete" size={16} />

// For edit operations - ALWAYS use 'edit'  
<Icon name="edit" size={16} />

// For add operations - ALWAYS use 'add'
<Icon name="add" size={16} />
```

## ❌ Forbidden Usage

```tsx
// DON'T DO THIS - Direct import forbidden
import { Trash, Trash2, Edit, Edit2 } from 'lucide-react';
```

## Icon Mapping

| Action | Icon Name | Lucide Component |
|--------|-----------|------------------|
| Delete | `delete` | `Trash2` |
| Edit | `edit` | `Edit3` |
| Add | `add` | `Plus` |
| Save | `save` | `Save` |
| Cancel | `cancel` | `X` |
| Search | `search` | `Search` |


## Adding New Icons

1. Add the icon to `src/components/icons/index.ts`
2. Update this documentation