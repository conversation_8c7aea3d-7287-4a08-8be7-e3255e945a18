import { Ch<PERSON><PERSON>Down, Loader2, Plus, X } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

export interface Option {
  value: string;
  label: string;
  disabled?: boolean;
  group?: string;
  icon?: React.ReactNode;
  description?: string;
}

export interface MultiSelectProps {
  options: Option[];
  value?: string[];
  defaultValue?: string[];
  onChange?: (value: string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyText?: string;
  maxSelected?: number;
  disabled?: boolean;
  clearable?: boolean;
  searchable?: boolean;
  creatable?: boolean;
  loading?: boolean;
  loadingText?: string;
  groupBy?: boolean;
  className?: string;
  variant?: "default" | "secondary" | "outline";
  size?: "sm" | "default" | "lg";
  onSearch?: (search: string) => void;
  onCreateOption?: (inputValue: string) => Option | Promise<Option>;
  renderOption?: (option: Option) => React.ReactNode;
  renderValue?: (option: Option) => React.ReactNode;
  validate?: (value: string[]) => string | undefined;
  error?: string;
  required?: boolean;
  name?: string;
  id?: string;
  selectAll?: boolean;
}

const MultiSelect = React.forwardRef<HTMLDivElement, MultiSelectProps>(
  (
    {
      options = [],
      value,
      defaultValue = [],
      onChange,
      placeholder = "Select options...",
      searchPlaceholder = "Search options...",
      emptyText = "No options found",
      maxSelected,
      disabled = false,
      clearable = true,
      searchable = true,
      creatable = false,
      loading = false,
      loadingText = "Loading...",
      groupBy = false,
      className,
      variant = "default",
      size = "default",
      onSearch,
      onCreateOption,
      renderOption,
      renderValue,
      validate,
      error,
      required = false,
      name,
      id,
      selectAll = true,
      ...props
    },
    ref
  ) => {
    const [open, setOpen] = React.useState(false);
    const [searchValue, setSearchValue] = React.useState("");
    const [internalValue, setInternalValue] =
      React.useState<string[]>(defaultValue);
    const [validationError, setValidationError] = React.useState<string>();
    const [isCreating, setIsCreating] = React.useState(false);

    const currentValue = value ?? internalValue;
    const isControlled = value !== undefined;

    const handleValueChange = React.useCallback(
      (newValue: string[]) => {
        if (!isControlled) {
          setInternalValue(newValue);
        }
        onChange?.(newValue);

        // Validate
        if (validate) {
          const validationResult = validate(newValue);
          setValidationError(validationResult);
        }
      },
      [isControlled, onChange, validate]
    );

    const selectedOptions = React.useMemo(() => {
      return options.filter((option) => currentValue.includes(option.value));
    }, [options, currentValue]);

    const availableOptions = React.useMemo(() => {
      let filtered = options;

      if (searchValue && searchable) {
        filtered = filtered.filter(
          (option) =>
            option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
            option.value.toLowerCase().includes(searchValue.toLowerCase()) ||
            option.description
              ?.toLowerCase()
              .includes(searchValue.toLowerCase())
        );
      }

      return filtered;
    }, [options, searchValue, searchable]);

    const groupedOptions = React.useMemo(() => {
      if (!groupBy) return { "": availableOptions };

      return availableOptions.reduce(
        (groups, option) => {
          const group = option.group || "Other";
          const newGroups = { ...groups };
          if (!newGroups[group]) newGroups[group] = [];
          newGroups[group].push(option);
          return newGroups;
        },
        {} as Record<string, Option[]>
      );
    }, [availableOptions, groupBy]);

    const handleToggleSelect = (optionValue: string) => {
      const isSelected = currentValue.includes(optionValue);

      if (isSelected) {
        const newValue = currentValue.filter((v) => v !== optionValue);
        handleValueChange(newValue);
      } else {
        if (maxSelected && currentValue.length >= maxSelected) return;
        const newValue = [...currentValue, optionValue];
        handleValueChange(newValue);
      }
    };

    const handleRemove = (optionValue: string) => {
      const newValue = currentValue.filter((v) => v !== optionValue);
      handleValueChange(newValue);
    };

    const handleClear = () => {
      handleValueChange([]);
    };

    const handleCreateOption = async () => {
      if (!creatable || !onCreateOption || !searchValue.trim()) return;

      setIsCreating(true);
      try {
        const newOption = await onCreateOption(searchValue.trim());
        handleToggleSelect(newOption.value);
        setSearchValue("");
      } catch (error) {
        toast.error(`Failed to create option: ${error}`);
      } finally {
        setIsCreating(false);
      }
    };

    const handleSearchChange = (search: string) => {
      setSearchValue(search);
      onSearch?.(search);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (
        event.key === "Backspace" &&
        !searchValue &&
        currentValue.length > 0
      ) {
        handleRemove(currentValue[currentValue.length - 1]);
      }
      if (event.key === "Escape") {
        setOpen(false);
      }
    };

    const sizeClasses = {
      sm: "h-8 text-xs",
      default: "h-10 text-sm",
      lg: "h-12 text-base",
    };

    const variantClasses = {
      default:
        "border-input bg-background hover:bg-accent hover:text-accent-foreground",
      secondary:
        "border-secondary bg-secondary text-secondary-foreground hover:bg-secondary/80",
      outline:
        "border-2 border-primary bg-background hover:bg-accent hover:text-accent-foreground",
    };

    const displayError = error || validationError;

    const selectableOptions = React.useMemo(() => {
      return availableOptions.filter((option) => !option.disabled);
    }, [availableOptions]);

    const isAllSelected = React.useMemo(() => {
      if (selectableOptions.length === 0) return false;
      return selectableOptions.every((option) =>
        currentValue.includes(option.value)
      );
    }, [selectableOptions, currentValue]);

    const isSomeSelected = React.useMemo(() => {
      return selectableOptions.some((option) =>
        currentValue.includes(option.value)
      );
    }, [selectableOptions, currentValue]);

    const handleSelectAll = () => {
      if (isAllSelected) {
        const newValue = currentValue.filter(
          (value) => !selectableOptions.some((option) => option.value === value)
        );
        handleValueChange(newValue);
      } else {
        const selectableValues = selectableOptions.map(
          (option) => option.value
        );
        let newValue = [...new Set([...currentValue, ...selectableValues])];

        if (maxSelected && newValue.length > maxSelected) {
          newValue = newValue.slice(0, maxSelected);
        }

        handleValueChange(newValue);
      }
    };

    const [triggerWidth, setTriggerWidth] = React.useState<number>();
    const triggerRef = React.useRef<HTMLButtonElement>(null);

    React.useEffect(() => {
      if (triggerRef.current) {
        setTriggerWidth(triggerRef.current.offsetWidth);
      }
    }, [open]);

    return (
      <div ref={ref} className={cn("relative", className)} {...props}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              ref={triggerRef}
              variant="outline"
              role="combobox"
              aria-expanded={open}
              aria-required={required}
              aria-invalid={!!displayError}
              aria-describedby={displayError ? `${id}-error` : undefined}
              className={cn(
                "w-full justify-between font-normal",
                sizeClasses[size],
                variantClasses[variant],
                displayError && "border-destructive focus:ring-destructive",
                disabled && "cursor-not-allowed opacity-50"
              )}
              disabled={disabled}
              id={id}
            >
              <div className="flex min-w-0 flex-1 items-center">
                {currentValue.length === 0 ? (
                  <span className="text-muted-foreground truncate">
                    {placeholder}
                  </span>
                ) : (
                  <div
                    className="flex min-w-0 flex-1 items-center gap-1 overflow-x-auto py-1 [&::-webkit-scrollbar]:hidden"
                    style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
                  >
                    {selectedOptions.map((option) => (
                      <Badge
                        key={option.value}
                        variant="secondary"
                        className="flex shrink-0 items-center gap-1 text-xs"
                      >
                        <span className="max-w-[100px] truncate">
                          {renderValue ? renderValue(option) : option.label}
                        </span>
                        {!disabled && (
                          <button
                            type="button"
                            className="cursor-pointer rounded-full p-1 hover:bg-red-100"
                            onMouseDown={(e) => e.preventDefault()}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemove(option.value);
                            }}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
              <div className="flex shrink-0 items-center gap-2">
                {clearable && currentValue.length > 0 && !disabled && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClear();
                    }}
                    className="cursor-pointer rounded-full p-1 hover:bg-red-100"
                  >
                    <X className="h-4 w-4 cursor-pointer" />
                  </button>
                )}
                <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
              </div>
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="p-0"
            align="start"
            style={{ width: triggerWidth }}
          >
            <Command onKeyDown={handleKeyDown}>
              {searchable && !disabled && (
                <div className="flex items-center border-b px-3">
                  <CommandInput
                    placeholder={searchPlaceholder}
                    value={searchValue}
                    onValueChange={handleSearchChange}
                    className="flex-1"
                    disabled={disabled}
                  />
                  {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                </div>
              )}
              <CommandList className="max-h-64">
                {loading ? (
                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    <span className="text-muted-foreground text-sm">
                      {loadingText}
                    </span>
                  </div>
                ) : (
                  <>
                    {selectAll && !disabled && selectableOptions.length > 0 && (
                      <>
                        <CommandGroup>
                          <CommandItem
                            onSelect={handleSelectAll}
                            className="flex items-center gap-2 font-medium"
                          >
                            <Checkbox
                              checked={isAllSelected}
                              ref={(node) => {
                                if (node) {
                                  const checkbox =
                                    node as unknown as HTMLInputElement;
                                  checkbox.indeterminate =
                                    isSomeSelected && !isAllSelected;
                                }
                              }}
                              className="h-4 w-4"
                            />
                            <span>Select All</span>
                          </CommandItem>
                        </CommandGroup>
                        <Separator />
                      </>
                    )}

                    {Object.entries(groupedOptions).map(
                      ([group, groupOptions]) => (
                        <React.Fragment key={group}>
                          {groupBy && group && (
                            <>
                              <div className="text-muted-foreground px-2 py-1.5 text-xs font-semibold">
                                {group}
                              </div>
                              <Separator />
                            </>
                          )}
                          <CommandGroup>
                            {groupOptions.map((option) => {
                              const isSelected = currentValue.includes(
                                option.value
                              );
                              return (
                                <CommandItem
                                  key={option.value}
                                  value={option.value}
                                  disabled={
                                    disabled ||
                                    option.disabled ||
                                    (maxSelected && !isSelected
                                      ? currentValue.length >= maxSelected
                                      : false)
                                  }
                                  onSelect={() =>
                                    !disabled &&
                                    handleToggleSelect(option.value)
                                  }
                                  className="flex items-center gap-2"
                                >
                                  <Checkbox
                                    checked={isSelected}
                                    className="h-4 w-4"
                                  />
                                  {option.icon && (
                                    <div className="shrink-0">
                                      {option.icon}
                                    </div>
                                  )}
                                  <div className="min-w-0 flex-1">
                                    {renderOption ? (
                                      renderOption(option)
                                    ) : (
                                      <div>
                                        <div className="truncate">
                                          {option.label}
                                        </div>
                                        {option.description && (
                                          <div className="text-muted-foreground truncate text-xs">
                                            {option.description}
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </CommandItem>
                              );
                            })}
                          </CommandGroup>
                        </React.Fragment>
                      )
                    )}

                    {creatable &&
                      !disabled &&
                      searchValue &&
                      !availableOptions.some(
                        (opt) =>
                          opt.label.toLowerCase() === searchValue.toLowerCase()
                      ) && (
                        <>
                          <Separator />
                          <CommandGroup>
                            <CommandItem
                              value={searchValue}
                              onSelect={handleCreateOption}
                              disabled={isCreating || disabled}
                              className="flex items-center gap-2"
                            >
                              {isCreating ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Plus className="h-4 w-4" />
                              )}
                              <span>Create &quot;{searchValue}&quot;</span>
                            </CommandItem>
                          </CommandGroup>
                        </>
                      )}

                    {!loading &&
                      availableOptions.length === 0 &&
                      !creatable && <CommandEmpty>{emptyText}</CommandEmpty>}
                  </>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {displayError && (
          <p id={`${id}-error`} className="text-destructive mt-1 text-xs">
            {displayError}
          </p>
        )}

        {name && (
          <input type="hidden" name={name} value={currentValue.join(",")} />
        )}
      </div>
    );
  }
);

MultiSelect.displayName = "MultiSelect";
export default MultiSelect;
