"use client";

import * as SelectPrimitive from "@radix-ui/react-select";
import { Check, ChevronDown, Loader2 } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

const SelectRoot = SelectPrimitive.Root;
const SelectValue = SelectPrimitive.Value;

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    size?: "small" | "default" | "large";
    variant?: "default" | "outline" | "ghost";
    error?: boolean;
  }
>(
  (
    {
      className,
      children,
      size = "default",
      variant = "default",
      error = false,
      ...props
    },
    ref
  ) => {
    const sizeClasses = {
      small: "h-7 px-2 text-xs",
      default: "h-9 px-3 text-sm",
      large: "h-11 px-4 text-base",
    };

    const variantClasses = {
      default:
        "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
      outline:
        "border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",
      ghost:
        "border-0 bg-transparent hover:bg-accent hover:text-accent-foreground",
    };

    return (
      <SelectPrimitive.Trigger
        ref={ref}
        className={cn(
          "ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex w-full items-center justify-between rounded-md text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
          sizeClasses[size],
          variantClasses[variant],
          error && "border-destructive focus:ring-destructive",
          className
        )}
        {...props}
      >
        {children}
        <SelectPrimitive.Icon asChild>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </SelectPrimitive.Icon>
      </SelectPrimitive.Trigger>
    );
  }
);
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4 rotate-180" />
  </SelectPrimitive.ScrollUpButton>
));
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
));
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName;

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          "p-1",
          position === "popper" &&
            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectContent.displayName = SelectPrimitive.Content.displayName;

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("py-1.5 pr-2 pl-8 text-sm font-semibold", className)}
    {...props}
  />
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {
    size?: "small" | "default" | "large";
  }
>(({ className, children, size = "default", ...props }, ref) => {
  const sizeClasses = {
    small: "py-1 pl-6 pr-2 text-xs",
    default: "py-1.5 pl-8 pr-2 text-sm",
    large: "py-2 pl-10 pr-3 text-base",
  };

  return (
    <SelectPrimitive.Item
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default items-center rounded-sm outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
        sizeClasses[size],
        className
      )}
      {...props}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <Check className="h-4 w-4" />
        </SelectPrimitive.ItemIndicator>
      </span>
      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
    </SelectPrimitive.Item>
  );
});
SelectItem.displayName = SelectPrimitive.Item.displayName;

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("bg-muted -mx-1 my-1 h-px", className)}
    {...props}
  />
));
SelectSeparator.displayName = SelectPrimitive.Separator.displayName;

// Types
interface SelectOption {
  label: string;
  value: string;
  name?: string;
  disabled?: boolean;
}

interface SelectProps {
  value?: string;
  onChange?: (event: {
    target: {
      name: string;
      value: string;
      label?: string;
      type: "select";
    };
  }) => void;
  name?: string;
  options?: SelectOption[];
  placeholder?: string;
  id?: string;
  className?: string;
  disabled?: boolean;
  error?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  isLoading?: boolean;
  size?: "small" | "default" | "large";
  variant?: "default" | "outline" | "ghost";
}

// Main Select Component
const SelectComponent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  SelectProps
>(
  (
    {
      value = "",
      onChange,
      name = "",
      options = [],
      placeholder = "Select",
      id,
      className = "",
      disabled = false,
      error = false,
      onFocus,
      onBlur,
      isLoading = false,
      size = "default",
      variant = "default",
      ...props
    },
    ref
  ) => {
    const selectedOption = options.find((option) => option.value === value);

    const handleValueChange = (selectedValue: string) => {
      const option = options.find((opt) => opt.value === selectedValue);
      if (onChange && option) {
        onChange({
          target: {
            name,
            value: selectedValue,
            label: option.label,
            type: "select",
          },
        });
      }
    };

    if (isLoading) {
      return (
        <div
          className={cn(
            "flex items-center justify-between rounded-md border px-3 py-2",
            className
          )}
        >
          <span className="text-muted-foreground">{placeholder}</span>
          <Loader2 className="h-4 w-4 animate-spin" />
        </div>
      );
    }

    return (
      <SelectPrimitive.Root
        value={value}
        onValueChange={handleValueChange}
        disabled={disabled}
        name={name}
        {...props}
      >
        <SelectTrigger
          ref={ref}
          className={className}
          size={size}
          variant={variant}
          error={error}
          id={id}
          onFocus={onFocus}
          onBlur={onBlur}
        >
          <SelectValue placeholder={placeholder}>
            {selectedOption?.label || selectedOption?.name}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {options.length === 0 ? (
            <div className="text-muted-foreground py-6 text-center text-sm">
              No options available
            </div>
          ) : (
            options.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                disabled={option.disabled}
                size={size}
              >
                {option.label || option.name}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </SelectPrimitive.Root>
    );
  }
);

SelectComponent.displayName = "Select";

const Select = Object.assign(SelectComponent, {
  Root: SelectRoot,
  Value: SelectValue,
  Trigger: SelectTrigger,
  Content: SelectContent,
  Item: SelectItem,
  Label: SelectLabel,
  Separator: SelectSeparator,
});

export default Select;
export type { SelectOption, SelectProps };
