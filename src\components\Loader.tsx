import { Loader2 } from "lucide-react";

import { cn } from "@/lib/utils";

export interface LoaderProps {
  variant?: "spinner" | "logo";
  size?: "sm" | "md" | "lg" | "xl";
  text?: string;
  showText?: boolean;
  className?: string;
  color?: "primary" | "secondary" | "muted" | "accent";
  fullScreen?: boolean;
  speed?: "slow" | "normal" | "fast";
}

const sizeClasses = {
  sm: "w-4 h-4",
  md: "w-6 h-6",
  lg: "w-8 h-8",
  xl: "w-12 h-12",
};

const textSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-base",
  xl: "text-lg",
};

const colorClasses = {
  primary: "text-primary",
  secondary: "text-secondary-foreground",
  muted: "text-muted-foreground",
  accent: "text-accent-foreground",
};

const speedClasses = {
  slow: "animate-spin [animation-duration:2s]",
  normal: "animate-spin [animation-duration:1s]",
  fast: "animate-spin [animation-duration:0.5s]",
};

const logoSizeClasses = {
  sm: "w-8 h-8",
  md: "w-12 h-12",
  lg: "w-16 h-16",
  xl: "w-24 h-24",
};

export default function Loader({
  variant = "spinner",
  size = "md",
  text = "Loading...",
  showText = true,
  className,
  color = "primary",
  fullScreen = false,
  speed = "normal",
}: LoaderProps) {
  const renderLoadingIndicator = () => {
    switch (variant) {
      case "spinner":
        return (
          <Loader2
            className={cn(
              sizeClasses[size],
              colorClasses[color],
              speedClasses[speed]
            )}
          />
        );

      case "logo":
        return (
          <div className="relative">
            <img
              src="/logo.png"
              alt="Loading"
              width={
                size === "sm"
                  ? 32
                  : size === "md"
                    ? 48
                    : size === "lg"
                      ? 64
                      : 96
              }
              height={
                size === "sm"
                  ? 32
                  : size === "md"
                    ? 48
                    : size === "lg"
                      ? 64
                      : 96
              }
              className={cn("animate-pulse", logoSizeClasses[size])}
              style={{
                animationDuration:
                  speed === "fast" ? "0.8s" : speed === "slow" ? "2s" : "1.2s",
              }}
            />
            <div
              className={cn(
                "absolute inset-0 animate-spin rounded-full border-2 border-transparent border-t-current",
                logoSizeClasses[size],
                colorClasses[color]
              )}
              style={{
                animationDuration:
                  speed === "fast" ? "0.5s" : speed === "slow" ? "2s" : "1s",
              }}
            />
          </div>
        );

      default:
        return null;
    }
  };

  const content = (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-3",
        fullScreen && "min-h-screen",
        className
      )}
    >
      {renderLoadingIndicator()}
      {showText && (
        <p
          className={cn(
            textSizeClasses[size],
            colorClasses[color],
            "animate-pulse"
          )}
        >
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="bg-background/80 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
        {content}
      </div>
    );
  }

  return content;
}

export function LoadingSpinner(props: Omit<LoaderProps, "variant">) {
  return <Loader {...props} variant="spinner" />;
}

export function LoadingLogo(props: Omit<LoaderProps, "variant">) {
  return <Loader {...props} variant="logo" />;
}

export function FullScreenLoading(props: Omit<LoaderProps, "fullScreen">) {
  return <Loader {...props} fullScreen />;
}
