import type { <PERSON>a, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Input } from "../../components/ui/input";

type StoryProps = ComponentProps<typeof Input>;

const meta: Meta<StoryProps> = {
  title: "UI/Input",
  component: Input,
  tags: ["autodocs"],
  args: {
    placeholder: "Type here...",
    disabled: false,
  },
};
export default meta;

type Story = StoryObj<StoryProps>;

export const Default: Story = {};
export const Password: Story = {
  args: { type: "password", placeholder: "Password" },
};
export const Disabled: Story = {
  args: { disabled: true, placeholder: "Disabled" },
};
export const Invalid: Story = {
  args: { placeholder: "Invalid", "aria-invalid": true },
};
