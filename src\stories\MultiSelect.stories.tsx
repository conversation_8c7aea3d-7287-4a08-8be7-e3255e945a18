import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ComponentProps, useState } from "react";

import MultiSelect from "../components/MultiSelect";

type StoryProps = ComponentProps<typeof MultiSelect>;

type Option = {
  value: string;
  label: string;
  group?: string;
};

const options: Option[] = [
  { value: "apple", label: "Apple", group: "Fruits" },
  { value: "banana", label: "Banana", group: "Fruits" },
  { value: "carrot", label: "Carrot", group: "Vegetables" },
  { value: "broccoli", label: "Broccoli", group: "Vegetables" },
  { value: "bread", label: "Bread", group: "Bakery" },
];

const meta: Meta<StoryProps> = {
  title: "MultiSelect",
  component: MultiSelect,
  tags: ["autodocs"],
  args: {
    options,
    placeholder: "Select items...",
    searchPlaceholder: "Search...",
    emptyText: "No options found",
    clearable: true,
    searchable: true,
    creatable: false,
    loading: false,
    variant: "default",
    size: "default",
    groupBy: true,
    selectAll: true,
  },
  argTypes: {
    size: { control: "select", options: ["sm", "default", "lg"] },
    variant: {
      control: "select",
      options: ["default", "secondary", "outline"],
    },
    value: { control: "object" },
    defaultValue: { control: "object" },
    maxSelected: { control: "number" },
  },
};

export default meta;
type Story = StoryObj<StoryProps>;

// Basic uncontrolled MultiSelect
export const Default: Story = {};

// Controlled MultiSelect
export const Controlled: Story = {
  render: (args) => {
    function ControlledWrapper(props: typeof args) {
      const [value, setValue] = useState<string[]>(["apple"]);
      return <MultiSelect {...props} value={value} onChange={setValue} />;
    }
    return <ControlledWrapper {...args} />;
  },
};

// Disabled
export const Disabled: Story = { args: { disabled: true } };

// With maxSelected
export const MaxSelected: Story = { args: { maxSelected: 2 } };

// Loading state
export const Loading: Story = {
  args: { loading: true, loadingText: "Loading items..." },
};

// Clearable & searchable
export const SearchableClearable: Story = {
  args: { clearable: true, searchable: true },
};

// Custom renderValue
export const CustomRenderValue: Story = {
  render: (args) => (
    <MultiSelect
      {...args}
      renderValue={(option) => (
        <span style={{ fontWeight: "bold" }}>{option.label}</span>
      )}
    />
  ),
};

// GroupBy false
export const NoGrouping: Story = { args: { groupBy: false } };
