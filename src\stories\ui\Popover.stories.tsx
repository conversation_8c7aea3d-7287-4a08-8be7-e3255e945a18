import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverAnchor,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

type StoryProps = ComponentProps<typeof Popover>;

const meta: Meta<StoryProps> = {
  title: "UI/Popover",
  component: Popover,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<StoryProps>;

export const Default: Story = {
  render: () => (
    <Popover>
      <PopoverTrigger>
        <Button variant="outline">Open Popover</Button>
      </PopoverTrigger>
      <PopoverContent>
        <p className="text-sm">This is a simple popover content.</p>
      </PopoverContent>
    </Popover>
  ),
};

export const WithAnchor: Story = {
  render: () => (
    <Popover>
      <PopoverAnchor>
        <span className="text-muted-foreground text-xs">I am the anchor</span>
      </PopoverAnchor>
      <PopoverTrigger>
        <Button variant="default">Trigger Popover</Button>
      </PopoverTrigger>
      <PopoverContent>
        <p className="text-sm">Popover anchored below the text.</p>
      </PopoverContent>
    </Popover>
  ),
};

export const CustomAlign: Story = {
  render: () => (
    <Popover>
      <PopoverTrigger>
        <Button variant="secondary">Aligned Popover</Button>
      </PopoverTrigger>
      <PopoverContent align="start" side="bottom">
        <p className="text-sm">Aligned to start, bottom side.</p>
      </PopoverContent>
    </Popover>
  ),
};
