import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import Loader, {
  FullScreenLoading,
  LoaderProps,
  LoadingLogo,
} from "@/components/Loader";

const meta: Meta<LoaderProps> = {
  title: "UI/Loader",
  component: Loader,
  tags: ["autodocs"],
  args: {
    variant: "spinner",
    size: "md",
    text: "Loading...",
    showText: true,
    color: "primary",
    fullScreen: false,
    speed: "normal",
    className: "",
  },
  argTypes: {
    variant: {
      control: "select",
      options: ["spinner", "logo"],
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg", "xl"],
    },
    color: {
      control: "select",
      options: ["primary", "secondary", "muted", "accent"],
    },
    speed: {
      control: "select",
      options: ["slow", "normal", "fast"],
    },
  },
};

export default meta;

type Story = StoryObj<LoaderProps>;

export const Default: Story = {};

export const Spinner: Story = {
  args: { variant: "spinner" },
};

export const Logo: Story = {
  args: { variant: "logo" },
};

export const WithoutText: Story = {
  args: { showText: false },
};

export const DifferentSizes: Story = {
  render: (args) => (
    <div className="flex items-center gap-4">
      <Loader {...args} size="sm" />
      <Loader {...args} size="md" />
      <Loader {...args} size="lg" />
      <Loader {...args} size="xl" />
    </div>
  ),
};

export const DifferentColors: Story = {
  render: (args) => (
    <div className="flex items-center gap-4">
      <Loader {...args} color="primary" />
      <Loader {...args} color="secondary" />
      <Loader {...args} color="muted" />
      <Loader {...args} color="accent" />
    </div>
  ),
};

export const DifferentSpeeds: Story = {
  render: (args) => (
    <div className="flex items-center gap-4">
      <Loader {...args} speed="slow" />
      <Loader {...args} speed="normal" />
      <Loader {...args} speed="fast" />
    </div>
  ),
};

export const FullScreen: Story = {
  render: (args) => <FullScreenLoading {...args} />,
};

export const LogoHelper: Story = {
  render: (args) => <LoadingLogo {...args} />,
};
