import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="OptiWise Design System" />



Welcome to the OptiWise Design System documentation. This Storybook contains all the components, patterns, and guidelines for building consistent user interfaces.

## Getting Started

Our design system is built with:
- **React 19** with TypeScript
- **Tailwind CSS** for styling

## Components

Browse through our component library to find:
- Basic UI components (buttons, inputs, etc.)
- Complex components (data tables, forms, etc.)
- Layout components
- Icons and illustrations

## Usage

Each component includes:
- Interactive examples
- Code snippets
- Props documentation
- Accessibility guidelines