import * as React from "react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export interface ModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string;
  children: React.ReactNode;

  showFooter?: boolean;
  footerContent?: React.ReactNode;

  onSubmit?: () => void | Promise<void>;
  submitText?: string;
  submitVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  submitDisabled?: boolean;

  showCancel?: boolean;
  cancelText?: string;
  onCancel?: () => void;

  isLoading?: boolean;

  className?: string;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  closeOnOverlayClick?: boolean;
  showCloseButton?: boolean;
}

const sizeClasses = {
  sm: "max-w-sm",
  md: "max-w-md",
  lg: "max-w-lg",
  xl: "max-w-xl",
  full: "max-w-full",
};

export function Modal({
  open,
  onOpenChange,
  title,
  description,
  children,
  showFooter = true,
  footerContent,
  onSubmit,
  submitText = "Submit",
  submitVariant = "default",
  submitDisabled = false,
  showCancel = true,
  cancelText = "Cancel",
  onCancel,
  isLoading = false,
  className,
  size = "md",
  closeOnOverlayClick = true,
  showCloseButton = true,
}: ModalProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async () => {
    if (!onSubmit || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await onSubmit();
    } catch (error) {
      toast.error(`Failed to submit: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      onOpenChange(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !closeOnOverlayClick) return;
    onOpenChange(newOpen);
  };

  const isDisabled = isLoading || isSubmitting || submitDisabled;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className={`${sizeClasses[size]} ${className || ""}`}
        showCloseButton={showCloseButton}
        onPointerDownOutside={(e) => {
          if (!closeOnOverlayClick) {
            e.preventDefault();
          }
        }}
      >
        {/* Header */}
        {(title || description) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && (
              <DialogDescription>{description}</DialogDescription>
            )}
          </DialogHeader>
        )}

        {/* Content */}
        <div className="py-4">{children}</div>

        {/* Footer */}
        {showFooter && (
          <DialogFooter>
            {footerContent || (
              <div className="flex justify-end gap-2">
                {showCancel && (
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isDisabled}
                  >
                    {cancelText}
                  </Button>
                )}
                {onSubmit && (
                  <Button
                    variant={submitVariant}
                    onClick={handleSubmit}
                    disabled={isDisabled}
                  >
                    {isSubmitting ? "Loading..." : submitText}
                  </Button>
                )}
              </div>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}

export default Modal;
