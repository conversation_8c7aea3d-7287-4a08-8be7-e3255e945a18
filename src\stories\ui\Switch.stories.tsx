import type { Meta, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Switch } from "../../components/ui/switch";

type StoryProps = ComponentProps<typeof Switch>;

const meta: Meta<StoryProps> = {
  title: "UI/Switch",
  component: Switch,
  tags: ["autodocs"],
  args: {
    checked: true,
    disabled: false,
  },
};
export default meta;

type Story = StoryObj<StoryProps>;

export const Off: Story = {
  args: { checked: false },
};
export const On: Story = { args: { checked: true } };
export const Disabled: Story = {
  args: { disabled: true, checked: true },
};
export const Invalid: Story = {
  args: { "aria-invalid": true, checked: false },
};
