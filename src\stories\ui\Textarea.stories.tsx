import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ComponentProps } from "react";

import { Textarea } from "../../components/ui/textarea";

type StoryProps = ComponentProps<typeof Textarea>;

const meta: Meta<StoryProps> = {
  title: "UI/Textarea",
  component: Textarea,
  tags: ["autodocs"],
  args: {
    placeholder: "Write something...",
  },
};
export default meta;

type Story = StoryObj<StoryProps>;

export const Default: Story = {};
export const Disabled: Story = { args: { disabled: true } };
export const Invalid: Story = { args: { "aria-invalid": true } };
