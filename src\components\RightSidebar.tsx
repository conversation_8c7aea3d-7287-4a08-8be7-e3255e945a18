import { <PERSON><PERSON>, <PERSON>, <PERSON>ader2, <PERSON><PERSON>, Save, Trash2, X } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  Drawer<PERSON><PERSON>er,
  Drawer<PERSON>eader,
  Drawer<PERSON>itle,
} from "@/components/ui/drawer";
import { cn } from "@/lib/utils";

interface ActionConfig {
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void | Promise<void>;
}

export interface RightSidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  children: React.ReactNode;

  showHeader?: boolean;
  headerContent?: React.ReactNode;
  showCloseButton?: boolean;

  showFooter?: boolean;
  footerContent?: React.ReactNode;

  onSave?: () => void | Promise<void>;
  onEdit?: () => void | Promise<void>;
  onCopy?: () => void | Promise<void>;
  onDelete?: () => void | Promise<void>;
  onPrint?: () => void | Promise<void>;
  onExport?: () => void | Promise<void>;
  onCancel?: () => void;

  customActions?: ActionConfig[];

  className?: string;
  width?: number;
  overlay?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  isLoading?: boolean;
  animationDuration?: number;
}

export function RightSidebar({
  open,
  onOpenChange,
  title,
  children,
  showHeader = true,
  headerContent,
  showCloseButton = true,
  showFooter = false,
  footerContent,
  onSave,
  onEdit,
  onCopy,
  onDelete,
  onPrint,
  onExport,
  onCancel,
  customActions = [],
  className,
  width = 400,
  overlay = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  isLoading = false,
  animationDuration = 300,
}: RightSidebarProps) {
  const [loadingStates, setLoadingStates] = React.useState<
    Record<string, boolean>
  >({});

  const handleAction = async (
    actionKey: string,
    actionFn: () => void | Promise<void>,
    errorMessage: string
  ): Promise<void> => {
    if (loadingStates[actionKey]) return;

    try {
      setLoadingStates((prev) => ({ ...prev, [actionKey]: true }));
      await actionFn();
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      toast.error(`${errorMessage}: ${message}`);
    } finally {
      setLoadingStates((prev) => ({ ...prev, [actionKey]: false }));
    }
  };

  const handleCancel = (): void => {
    if (onCancel) {
      onCancel();
    } else {
      onOpenChange(false);
    }
  };

  const isAnyActionLoading = Object.values(loadingStates).some(Boolean);

  return (
    <Drawer
      open={open}
      onOpenChange={closeOnOverlayClick ? onOpenChange : undefined}
      direction="right"
      dismissible={closeOnOverlayClick}
      modal={overlay}
    >
      <DrawerContent
        className={cn(
          "h-full [&[data-vaul-drawer-direction=right]]:w-auto [&[data-vaul-drawer-direction=right]]:max-w-none",
          className
        )}
        style={{
          width: `${width}px !important`,
          animationDuration: `${animationDuration}ms`,
        }}
        onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
      >
        {showHeader && (title || headerContent || showCloseButton) && (
          <DrawerHeader className="flex flex-row items-center justify-between border-b px-3 py-2">
            <div className="min-w-0 flex-1">
              {headerContent || (
                <div>
                  {title && (
                    <DrawerTitle className="truncate">{title}</DrawerTitle>
                  )}
                </div>
              )}
            </div>

            {/* Action Buttons in Header */}
            <div className="ml-4 flex items-center gap-1">
              {onSave && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleAction("save", onSave, "Failed to save")}
                  disabled={isLoading || isAnyActionLoading}
                  className="h-8 w-8"
                >
                  {loadingStates.save ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span className="sr-only">Save</span>
                </Button>
              )}

              {onEdit && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleAction("edit", onEdit, "Failed to edit")}
                  disabled={isLoading || isAnyActionLoading}
                  className="h-8 w-8"
                >
                  {loadingStates.edit ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Edit className="h-4 w-4" />
                  )}
                  <span className="sr-only">Edit</span>
                </Button>
              )}

              {onCopy && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleAction("copy", onCopy, "Failed to copy")}
                  disabled={isLoading || isAnyActionLoading}
                  className="h-8 w-8"
                >
                  {loadingStates.copy ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  <span className="sr-only">Copy</span>
                </Button>
              )}

              {onPrint && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    handleAction("print", onPrint, "Failed to print")
                  }
                  disabled={isLoading || isAnyActionLoading}
                  className="h-8 w-8"
                >
                  {loadingStates.print ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Printer className="h-4 w-4" />
                  )}
                  <span className="sr-only">Print</span>
                </Button>
              )}

              {onExport && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    handleAction("export", onExport, "Failed to export")
                  }
                  disabled={isLoading || isAnyActionLoading}
                  className="h-8 w-8"
                >
                  {loadingStates.export ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span className="sr-only">Export</span>
                </Button>
              )}

              {customActions.map((action, index) => {
                const actionKey = `custom-${index}`;
                return (
                  <Button
                    key={actionKey}
                    variant="ghost"
                    size="icon"
                    onClick={() =>
                      handleAction(actionKey, action.onClick, "Action failed")
                    }
                    disabled={isLoading || isAnyActionLoading}
                    className="h-8 w-8"
                  >
                    {loadingStates[actionKey] ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <action.icon className="h-4 w-4" />
                    )}
                    <span className="sr-only">Custom Action</span>
                  </Button>
                );
              })}

              {onDelete && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    handleAction("delete", onDelete, "Failed to delete")
                  }
                  disabled={isLoading || isAnyActionLoading}
                  className="text-destructive hover:text-destructive h-8 w-8"
                >
                  {loadingStates.delete ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                  <span className="sr-only">Delete</span>
                </Button>
              )}

              {showCloseButton && (
                <DrawerClose asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleCancel}
                    className="ml-2 h-8 w-8"
                    disabled={isLoading || isAnyActionLoading}
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close</span>
                  </Button>
                </DrawerClose>
              )}
            </div>
          </DrawerHeader>
        )}

        <div className="flex-1 overflow-y-auto p-6">{children}</div>

        {(showFooter || footerContent) && (
          <DrawerFooter className="border-t p-6">{footerContent}</DrawerFooter>
        )}
      </DrawerContent>
    </Drawer>
  );
}
